<template>
  <view 
    class="meditation-card" 
    :class="{ 'grid-card': isGridItem }"
    @click="onClick"
  >
    <image class="card-cover" :src="cover" mode="aspectFill" />
    <view class="card-content">
      <text class="card-title">{{ title }}</text>
      <text class="card-learners">{{ learners }}人正在学</text>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  id: string
  title: string
  cover: string
  learners: number
  isGridItem?: boolean
}

interface Emits {
  (e: 'click', id: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const onClick = () => {
  emit('click', props.id)
}
</script>

<style scoped>
.meditation-card {
  width: 240rpx;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-right: 24rpx;
}

.card-cover {
  width: 100%;
  height: 160rpx;
  object-fit: cover;
}

.card-content {
  padding: 16rpx;
}

.card-title {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-learners {
  font-size: 22rpx;
  color: #718096;
}

.grid-card {
  width: 100%;
  margin-right: 0;
}
</style>