# 微信小程序云开发配置指南

## 概述

本项目使用微信小程序云开发实现用户登录和数据存储功能。通过云函数处理用户认证，使用云数据库存储用户信息。

## 云开发环境配置

### 1. 创建云开发环境

1. 在微信开发者工具中打开项目
2. 点击工具栏中的"云开发"按钮
3. 创建新的云开发环境，环境ID设置为：`double-meditation-dev`
4. 等待环境创建完成

### 2. 配置云函数

本项目包含以下云函数：

#### login 云函数
- **功能**：处理用户登录，获取openid，存储用户信息
- **位置**：`cloudfunctions/login/`
- **部署**：右键点击login文件夹，选择"上传并部署：云端安装依赖"

#### getUserInfo 云函数
- **功能**：获取和更新用户信息，管理用户统计数据
- **位置**：`cloudfunctions/getUserInfo/`
- **部署**：右键点击getUserInfo文件夹，选择"上传并部署：云端安装依赖"

### 3. 数据库集合

项目会自动创建以下数据库集合：

#### users 集合
存储用户基本信息和统计数据：

```json
{
  "_id": "自动生成的文档ID",
  "openid": "用户的微信openid",
  "appid": "小程序appid",
  "unionid": "用户unionid（可选）",
  "nickname": "用户昵称",
  "avatar": "用户头像URL",
  "gender": "用户性别（0未知，1男，2女）",
  "city": "用户城市",
  "province": "用户省份",
  "country": "用户国家",
  "language": "用户语言",
  "succulentCount": "多肉数量",
  "meditationLevel": "冥想等级",
  "totalDays": "坚持天数",
  "weekData": "本周数据",
  "createTime": "创建时间",
  "lastLoginTime": "最后登录时间",
  "updateTime": "更新时间"
}
```

## 使用方法

### 1. 初始化云开发

```typescript
import { initCloud } from '@/utils/cloud'

// 在应用启动时初始化
initCloud()
```

### 2. 用户登录

```typescript
import { wechatLogin, getWechatUserInfo } from '@/utils/auth'

// 微信登录
const login = async () => {
  try {
    // 先获取登录凭证
    await wechatLogin()
    
    // 获取用户信息并存储到云数据库
    const userInfo = await getWechatUserInfo()
    console.log('登录成功:', userInfo)
  } catch (error) {
    console.error('登录失败:', error)
  }
}
```

### 3. 获取用户信息

```typescript
import { getCloudUserInfo } from '@/utils/auth'

// 从云端获取最新用户信息
const getUserInfo = async () => {
  try {
    const userInfo = await getCloudUserInfo()
    console.log('用户信息:', userInfo)
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}
```

### 4. 更新用户统计数据

```typescript
import { updateUserStats } from '@/utils/auth'

// 更新用户统计数据
const updateStats = async () => {
  try {
    await updateUserStats({
      succulentCount: 15,
      meditationLevel: 6,
      totalDays: 30
    })
    console.log('统计数据更新成功')
  } catch (error) {
    console.error('更新统计数据失败:', error)
  }
}
```

## 注意事项

1. **环境ID配置**：确保 `project.config.json` 和代码中的环境ID一致
2. **权限配置**：确保 `manifest.json` 中已配置必要的权限
3. **云函数部署**：每次修改云函数后需要重新部署
4. **数据库权限**：建议设置适当的数据库读写权限
5. **错误处理**：生产环境中要做好错误处理和用户提示

## 开发调试

1. 在微信开发者工具中查看云开发控制台
2. 查看云函数日志排查问题
3. 使用数据库管理界面查看数据
4. 测试不同场景下的登录流程

## 部署上线

1. 确保所有云函数都已部署到生产环境
2. 检查数据库权限设置
3. 测试完整的用户登录流程
4. 监控云函数调用情况和错误日志
