<template>
  <view class="search-box">
    <view class="search-input-wrapper" @click="onSearchBoxClick">
      <view class="search-icon">🔍</view>
      <view class="search-input">{{ placeholder }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '搜索冥想课程...'
})

const onSearchBoxClick = () => {
  uni.navigateTo({
    url: '/pages/search/search'
  })
}
</script>

<style scoped>
.search-box {
  padding: 24rpx 32rpx;
  background: #F7FAFC;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 48rpx;
  padding: 0 32rpx;
  height: 88rpx;
  cursor: pointer;
}

.search-icon {
  font-size: 32rpx;
  color: #a0aec0;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #a0aec0;
}
</style>