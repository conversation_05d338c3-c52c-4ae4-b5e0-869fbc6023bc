# 登录功能测试指南

## 测试前准备

### 1. 云开发环境配置
- 确保已在微信开发者工具中创建云开发环境
- 环境ID设置为：`double-meditation-dev`
- 云函数已成功部署

### 2. 项目配置检查
- `project.config.json` 中的云开发配置正确
- `manifest.json` 中的权限配置完整
- 小程序appid配置正确

## 测试步骤

### 1. 基础登录测试

1. **启动项目**
   ```bash
   npm run dev:mp-weixin
   ```

2. **打开微信开发者工具**
   - 导入项目
   - 选择小程序项目
   - 确保云开发环境已连接

3. **测试未登录状态**
   - 进入"我的"页面
   - 确认显示"点击登录"状态
   - 头像显示默认占位图
   - 用户信息显示未登录状态

4. **测试登录流程**
   - 点击头像区域
   - 确认弹出授权弹窗
   - 点击"允许"授权
   - 观察登录过程中的loading状态
   - 确认登录成功提示

5. **验证登录结果**
   - 用户头像更新为微信头像
   - 用户昵称显示正确
   - 用户ID显示为openid
   - 统计数据正确显示

### 2. 云数据库验证

1. **查看数据库记录**
   - 在微信开发者工具中打开云开发控制台
   - 进入数据库管理
   - 查看users集合
   - 确认用户记录已创建

2. **验证数据完整性**
   - openid字段正确
   - 用户基本信息完整
   - 创建时间和登录时间正确
   - 统计数据初始化正确

### 3. 功能测试

1. **刷新用户信息**
   - 登录后点击头像
   - 选择"刷新用户信息"
   - 确认数据更新成功

2. **退出登录**
   - 点击头像选择"退出登录"
   - 确认退出确认弹窗
   - 点击确认退出
   - 验证页面状态重置

3. **重新登录**
   - 退出后重新登录
   - 确认用户信息正确恢复
   - 验证数据库中lastLoginTime更新

### 4. 异常情况测试

1. **网络异常**
   - 断开网络连接
   - 尝试登录
   - 确认错误提示正确

2. **权限拒绝**
   - 拒绝用户信息授权
   - 确认错误处理正确

3. **云函数异常**
   - 临时删除云函数
   - 测试错误处理

## 预期结果

### 成功登录后的状态
- 用户头像：显示微信真实头像
- 用户昵称：显示微信昵称
- 用户ID：显示以"wx"开头的openid
- 多肉数量：初始为0
- 冥想等级：初始为1
- 坚持天数：初始为0

### 数据库记录
```json
{
  "_id": "自动生成",
  "openid": "用户openid",
  "nickname": "用户昵称",
  "avatar": "头像URL",
  "gender": 0,
  "city": "",
  "province": "",
  "country": "",
  "language": "zh_CN",
  "succulentCount": 0,
  "meditationLevel": 1,
  "totalDays": 0,
  "createTime": "创建时间",
  "lastLoginTime": "最后登录时间"
}
```

## 常见问题排查

### 1. 登录失败
- 检查云开发环境是否正确配置
- 确认云函数是否部署成功
- 查看云函数调用日志

### 2. 用户信息获取失败
- 检查getUserProfile权限配置
- 确认用户是否授权
- 查看控制台错误信息

### 3. 数据库写入失败
- 检查数据库权限配置
- 确认云函数权限设置
- 查看数据库操作日志

### 4. 页面状态异常
- 检查本地存储数据
- 确认状态管理逻辑
- 重启小程序重新测试

## 性能测试

1. **登录速度**
   - 记录从点击到完成的时间
   - 目标：3秒内完成

2. **数据同步**
   - 测试云端数据同步速度
   - 确认数据一致性

3. **离线处理**
   - 测试离线状态下的行为
   - 确认缓存机制正常

## 测试报告模板

```
测试时间：
测试环境：
测试结果：
- 基础登录：✅/❌
- 数据存储：✅/❌
- 状态管理：✅/❌
- 异常处理：✅/❌

发现问题：
1. 问题描述
2. 复现步骤
3. 预期结果
4. 实际结果

建议改进：
1. 改进建议
2. 优化方向
```
