<template>
  <view class="recommend-card" @click="onCardClick">
    <image class="card-cover" :src="cover" mode="aspectFill" />
    <view class="card-content">
      <text class="card-title">{{ title }}</text>
      <text class="card-desc">{{ description }}</text>
      <text class="card-duration">{{ duration }}分钟</text>
    </view>
    <view class="add-button" @click.stop="onAddToPlan">
      <text class="add-text">加入计划</text>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  id: string
  title: string
  description: string
  cover: string
  duration: number
}

interface Emits {
  (e: 'click', id: string): void
  (e: 'add-to-plan', id: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const onCardClick = () => {
  emit('click', props.id)
}

const onAddToPlan = () => {
  emit('add-to-plan', props.id)
}
</script>

<style scoped>
.recommend-card {
  width: 280rpx;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-right: 24rpx;
}

.card-cover {
  width: 100%;
  height: 160rpx;
  object-fit: cover;
}

.card-content {
  padding: 16rpx;
}

.card-title {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-desc {
  display: block;
  font-size: 22rpx;
  color: #718096;
  margin-bottom: 8rpx;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-duration {
  font-size: 22rpx;
  color: #7fb069;
  margin-bottom: 16rpx;
}

.add-button {
  width: 100%;
  height: 64rpx;
  background: #7fb069;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #ffffff;
}
</style>