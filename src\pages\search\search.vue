<template>
  <view class="search-page">
    <NavBar title="搜索" />

    <!-- 搜索输入框 -->
    <view class="search-input-container">
      <view class="search-input-wrapper">
        <view class="search-icon">🔍</view>
        <input class="search-input" type="text" placeholder="搜索冥想课程..." v-model="searchKeyword" @confirm="onSearch"
          focus />
        <view class="search-btn" @click="onSearch">搜索</view>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view v-if="hasSearched" class="search-results">
      <!-- 分类标签 -->
      <scroll-view class="category-tabs" scroll-x="true">
        <view class="tabs">
          <view v-for="(category, index) in categories" :key="category.id" class="tab-item"
            :class="{ active: activeCategoryIndex === index }" @click="onCategoryClick(index)">
            {{ category.name }}
          </view>
        </view>
      </scroll-view>

      <!-- 结果列表 -->
      <scroll-view class="results-content" scroll-y="true">
        <view v-if="currentResults.length > 0" class="results-grid">
          <ContentCard v-for="item in currentResults" :key="item.id" :id="item.id" :title="item.title"
            :cover="item.cover" :learners="item.learners" :category="item.category" @click="onItemClick" />
        </view>
        <view v-else class="no-results">
          <text class="no-results-text">暂无搜索结果</text>
        </view>
      </scroll-view>
    </view>

    <!-- 搜索建议 -->
    <view v-else class="search-suggestions">
      <view class="suggestions-title">热门搜索</view>
      <view class="suggestions-list">
        <view v-for="suggestion in hotSearches" :key="suggestion" class="suggestion-item"
          @click="onSuggestionClick(suggestion)">
          {{ suggestion }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import NavBar from '@/components/NavBar.vue'
import ContentCard from '@/components/ContentCard.vue'

// 搜索相关
const searchKeyword = ref('')
const hasSearched = ref(false)
const activeCategoryIndex = ref(0)

// 分类数据
const categories = ref([
  { id: 'all', name: '全部' },
  { id: 'meditation', name: '冥想' },
  { id: 'sleep', name: '睡眠' },
  { id: 'sound', name: '声音' }
])

// 热门搜索
const hotSearches = ref([
  '正念冥想', '深度睡眠', '压力释放', '专注力', '情绪管理', '白噪音'
])

// 模拟搜索结果数据
const searchResults = ref({
  all: [
    { id: '101', title: '呼吸觉察冥想', cover: 'https://picsum.photos/240/160?random=101', learners: 1234, category: '冥想' },
    { id: '201', title: '深度放松睡眠', cover: 'https://picsum.photos/240/160?random=201', learners: 2156, category: '睡眠' },
    { id: '501', title: '雨声白噪音', cover: 'https://picsum.photos/240/160?random=501', learners: 3456, category: '声音' },
    { id: '102', title: '身体扫描冥想', cover: 'https://picsum.photos/240/160?random=102', learners: 856, category: '冥想' },
    { id: '202', title: '渐进式肌肉放松', cover: 'https://picsum.photos/240/160?random=202', learners: 1789, category: '睡眠' },
    { id: '502', title: '海浪声音', cover: 'https://picsum.photos/240/160?random=502', learners: 2876, category: '声音' }
  ],
  meditation: [
    { id: '101', title: '呼吸觉察冥想', cover: 'https://picsum.photos/240/160?random=101', learners: 1234, category: '冥想' },
    { id: '102', title: '身体扫描冥想', cover: 'https://picsum.photos/240/160?random=102', learners: 856, category: '冥想' },
    { id: '103', title: '行走冥想', cover: 'https://picsum.photos/240/160?random=103', learners: 672, category: '冥想' },
    { id: '301', title: '单点专注冥想', cover: 'https://picsum.photos/240/160?random=301', learners: 1098, category: '冥想' }
  ],
  sleep: [
    { id: '201', title: '深度放松睡眠', cover: 'https://picsum.photos/240/160?random=201', learners: 2156, category: '睡眠' },
    { id: '202', title: '渐进式肌肉放松', cover: 'https://picsum.photos/240/160?random=202', learners: 1789, category: '睡眠' },
    { id: '203', title: '睡前故事', cover: 'https://picsum.photos/240/160?random=203', learners: 1432, category: '睡眠' },
    { id: '204', title: '白噪音睡眠', cover: 'https://picsum.photos/240/160?random=204', learners: 987, category: '睡眠' }
  ],
  sound: [
    { id: '501', title: '雨声白噪音', cover: 'https://picsum.photos/240/160?random=501', learners: 3456, category: '声音' },
    { id: '502', title: '海浪声音', cover: 'https://picsum.photos/240/160?random=502', learners: 2876, category: '声音' },
    { id: '503', title: '森林鸟鸣', cover: 'https://picsum.photos/240/160?random=503', learners: 2543, category: '声音' },
    { id: '509', title: '西藏颂钵', cover: 'https://picsum.photos/240/160?random=509', learners: 2567, category: '声音' }
  ]
})

const currentResults = computed(() => {
  const currentCategory = categories.value[activeCategoryIndex.value]
  return searchResults.value[currentCategory.id as keyof typeof searchResults.value] || []
})

// 事件处理
const onSearch = () => {
  if (searchKeyword.value.trim()) {
    hasSearched.value = true
    // 这里可以添加实际的搜索逻辑
    console.log('搜索:', searchKeyword.value)
  }
}

const onCategoryClick = (index: number) => {
  activeCategoryIndex.value = index
}

const onSuggestionClick = (suggestion: string) => {
  searchKeyword.value = suggestion
  onSearch()
}

const onItemClick = (id: string) => {
  uni.navigateTo({
    url: `/pages/meditation-detail/meditation-detail?id=${id}`
  })
}
</script>

<style scoped>
.search-page {
  padding-top: 176rpx;
  min-height: 100vh;
  background-color: #F7FAFC;
}

.search-input-container {
  padding: 24rpx 32rpx;
  background: transparent;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 48rpx;
  padding: 0 32rpx;
  height: 88rpx;
}

.search-icon {
  font-size: 32rpx;
  color: #a0aec0;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #2d3748;
  background: transparent;
  border: none;
}

.search-btn {
  padding: 16rpx 24rpx;
  background: #7FB069;
  color: #ffffff;
  border-radius: 32rpx;
  font-size: 28rpx;
  margin-left: 16rpx;
}

.search-results {
  flex: 1;
}

.category-tabs {
  /* padding: 24rpx 0; */
}

.tabs {
  display: flex;
  padding: 0 32rpx;
  white-space: nowrap;
}

.tab-item {
  padding: 16rpx 32rpx;
  margin-right: 24rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  color: #718096;
  background: #f7fafc;
  white-space: nowrap;
  transition: all 0.3s;
}

.tab-item.active {
  background: #7FB069;
  color: #ffffff;
}

.results-content {
  flex: 1;
  padding: 32rpx;
  width: calc(100% - 64rpx);
}

.results-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  width: 100%;
}

.results-grid :deep(.content-card) {
  width: 100%;
}

.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.no-results-text {
  font-size: 28rpx;
  color: #a0aec0;
}

.search-suggestions {
  padding: 32rpx;
}

.suggestions-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 24rpx;
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
}

.suggestion-item {
  padding: 16rpx 24rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  background: #ffffff;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #718096;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
</style>