<template>
  <view class="meditation-section">
    <view class="section-header">
      <text class="section-title">{{ title }}</text>
      <text class="view-all" @click="onViewAll">{{ viewAllText || '查看全部' }}</text>
    </view>
    
    <!-- 普通横向滑动布局 -->
    <scroll-view 
      v-if="!isMoreSection"
      class="cards-scroll" 
      scroll-x="true" 
      show-scrollbar="false"
    >
      <view class="cards-container">
        <MeditationCard
          v-for="item in items"
          :key="item.id"
          :id="item.id"
          :title="item.title"
          :cover="item.cover"
          :learners="item.learners"
          @click="onCardClick"
        />
      </view>
    </scroll-view>
    
    <!-- 更多课程垂直网格布局 -->
    <view v-else class="more-section">
      <view class="grid-container">
        <MeditationCard
          v-for="item in items"
          :key="item.id"
          :id="item.id"
          :title="item.title"
          :cover="item.cover"
          :learners="item.learners"
          :is-grid-item="true"
          @click="onCardClick"
        />
      </view>
      <view class="view-all-button" @click="onViewAll">
        <text class="button-text">查看全部</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import MeditationCard from './MeditationCard.vue'

interface MeditationItem {
  id: string
  title: string
  cover: string
  learners: number
}

interface Props {
  title: string
  items: MeditationItem[]
  viewAllText?: string
  isMoreSection?: boolean
}

interface Emits {
  (e: 'view-all', title: string): void
  (e: 'card-click', id: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const onViewAll = () => {
  emit('view-all', props.title)
}

const onCardClick = (id: string) => {
  emit('card-click', id)
}
</script>

<style scoped>
.meditation-section {
  margin-bottom: 48rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
}

.view-all {
  font-size: 26rpx;
  color: #7fb069;
}

.cards-scroll {
  white-space: nowrap;
}

/* 隐藏水平滚动条 */
.cards-scroll::-webkit-scrollbar {
  display: none;
}

.cards-scroll {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.cards-container {
  display: flex;
  padding: 0 32rpx;
}

.more-section {
  padding: 0 32rpx;
}

.grid-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.view-all-button {
  width: 100%;
  height: 88rpx;
  background: #7fb069;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
}
</style>