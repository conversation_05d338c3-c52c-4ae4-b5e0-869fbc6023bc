// 云开发初始化和工具函数

// 声明微信小程序全局对象
declare const wx: any;

// 云开发环境配置
export const CLOUD_ENV = 'cloud1-6gxijqna174103d8';

// 初始化云开发
export const initCloud = () => {
  // #ifdef MP-WEIXIN
  if (typeof wx !== 'undefined' && wx.cloud) {
    try {
      wx.cloud.init({
        env: CLOUD_ENV,
        traceUser: true
      });
      console.log('云开发初始化成功');
    } catch (error) {
      console.error('云开发初始化失败:', error);
    }
  }
  // #endif
};

// 调用云函数的通用方法
export const callCloudFunction = (name: string, data: any = {}): Promise<any> => {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    if (typeof wx !== 'undefined' && wx.cloud) {
      wx.cloud.callFunction({
        name,
        data,
        success: (res: any) => {
          resolve(res.result);
        },
        fail: (error: any) => {
          console.error(`调用云函数 ${name} 失败:`, error);
          reject(error);
        }
      });
    } else {
      reject(new Error('云开发未初始化'));
    }
    // #endif

    // #ifndef MP-WEIXIN
    // 非微信小程序环境，返回模拟数据
    console.log(`模拟调用云函数 ${name}:`, data);
    resolve({ success: true, message: '模拟调用成功' });
    // #endif
  });
};

// 云数据库操作
export const cloudDB = {
  // 获取数据库引用
  database: () => {
    // #ifdef MP-WEIXIN
    if (typeof wx !== 'undefined' && wx.cloud) {
      return wx.cloud.database();
    }
    // #endif
    return null;
  },

  // 获取集合引用
  collection: (name: string) => {
    const db = cloudDB.database();
    return db ? db.collection(name) : null;
  }
};

// 云存储操作
export const cloudStorage = {
  // 上传文件
  uploadFile: (cloudPath: string, filePath: string): Promise<any> => {
    return new Promise((resolve, reject) => {
      // #ifdef MP-WEIXIN
      if (typeof wx !== 'undefined' && wx.cloud) {
        wx.cloud.uploadFile({
          cloudPath,
          filePath,
          success: resolve,
          fail: reject
        });
      } else {
        reject(new Error('云开发未初始化'));
      }
      // #endif

      // #ifndef MP-WEIXIN
      console.log(`模拟上传文件到云存储: ${cloudPath}`);
      resolve({ fileID: `mock_file_id_${Date.now()}` });
      // #endif
    });
  },

  // 下载文件
  downloadFile: (fileID: string): Promise<any> => {
    return new Promise((resolve, reject) => {
      // #ifdef MP-WEIXIN
      if (typeof wx !== 'undefined' && wx.cloud) {
        wx.cloud.downloadFile({
          fileID,
          success: resolve,
          fail: reject
        });
      } else {
        reject(new Error('云开发未初始化'));
      }
      // #endif

      // #ifndef MP-WEIXIN
      console.log(`模拟从云存储下载文件: ${fileID}`);
      resolve({ tempFilePath: '/mock/file/path' });
      // #endif
    });
  },

  // 删除文件
  deleteFile: (fileList: string[]): Promise<any> => {
    return new Promise((resolve, reject) => {
      // #ifdef MP-WEIXIN
      if (typeof wx !== 'undefined' && wx.cloud) {
        wx.cloud.deleteFile({
          fileList,
          success: resolve,
          fail: reject
        });
      } else {
        reject(new Error('云开发未初始化'));
      }
      // #endif

      // #ifndef MP-WEIXIN
      console.log(`模拟删除云存储文件:`, fileList);
      resolve({ fileList: fileList.map(id => ({ fileID: id, status: 0 })) });
      // #endif
    });
  }
};
