<template>
  <view class="plan-management-page">
    <NavBar title="计划管理" :show-back="true" />
    <scroll-view class="content" scroll-y="true">
      <view class="plan-list">
        <view v-for="(plan, index) in plans" :key="plan.id" class="plan-item">
          <view class="plan-info">
            <text class="plan-course">{{ plan.courseName }}</text>
            <text class="plan-lesson">{{ plan.lessonName }}</text>
          </view>
          <view class="plan-actions">
            <text class="action-btn edit-btn" @click="onEdit(plan.id, index)">编辑</text>
            <text class="action-btn delete-btn" @click="onDelete(plan.id, index)">删除</text>
          </view>
        </view>
      </view>
      <view class="add-plan-button" @click="onAdd">
        <text class="button-text">添加新计划</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import NavBar from '@/components/NavBar.vue'

// 定义计划项类型
interface PlanItem {
  id: string
  courseName: string
  lessonName: string
  cover: string
  duration: number
  progress: number
  learnersCount: number
  isCompleted: boolean
}

// 示例计划数据
const plans = ref<PlanItem[]>([
  {
    id: '0_1',
    courseName: '正念冥想基础课',
    lessonName: '专注当下练习',
    cover: 'https://picsum.photos/120/120?random=5',
    duration: 20,
    progress: 100,
    learnersCount: 1234,
    isCompleted: true
  },
  {
    id: '0_2',
    courseName: '身心放松课程',
    lessonName: '深度身体扫描',
    cover: 'https://picsum.photos/120/120?random=6',
    duration: 15,
    progress: 65,
    learnersCount: 987,
    isCompleted: false
  },
  {
    id: '0_3',
    courseName: '睡眠改善课程',
    lessonName: '睡前放松冥想',
    cover: 'https://picsum.photos/120/120?random=7',
    duration: 12,
    progress: 0,
    learnersCount: 1567,
    isCompleted: false
  }
])

const onEdit = (id: string, index: number) => {
  console.log(`编辑计划: ${id}, index: ${index}`)
  const plan = plans.value[index]
  uni.navigateTo({
    url: `/pages/plan-edit/plan-edit?id=${id}&courseName=${plan.courseName}&lessonName=${plan.lessonName}`
  })
}

const onDelete = (id: string, index: number) => {
  console.log(`删除计划: ${id}, index: ${index}`)
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个计划吗？',
    success: (res) => {
      if (res.confirm) {
        plans.value.splice(index, 1)
        uni.showToast({ title: '已删除', icon: 'success' })
      }
    }
  })
}

const onAdd = () => {
  console.log('添加新计划')
  uni.navigateTo({ url: '/pages/plan-edit/plan-edit' })
}

</script>

<style scoped>
.plan-management-page {
  padding-top: 176rpx;
  min-height: 100vh;
  background-color: #F7FAFC;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding: 32rpx;
}

.plan-list {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.plan-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #EDF2F7;
}

.plan-item:last-child {
  border-bottom: none;
}

.plan-info {
  display: flex;
  flex-direction: column;
}

.plan-course {
  font-size: 28rpx;
  font-weight: 500;
  color: #2D3748;
  margin-bottom: 8rpx;
}

.plan-lesson {
  font-size: 24rpx;
  color: #718096;
}

.plan-actions {
  display: flex;
}

.action-btn {
  font-size: 24rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  margin-left: 16rpx;
}

.edit-btn {
  color: #4A5568;
  background-color: #EDF2F7;
}

.delete-btn {
  color: #C53030;
  background-color: #FED7D7;
}

.add-plan-button {
  margin-top: 48rpx;
  height: 96rpx;
  background: #7FB069;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(127, 176, 105, 0.3);
}

.button-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
}
</style>
