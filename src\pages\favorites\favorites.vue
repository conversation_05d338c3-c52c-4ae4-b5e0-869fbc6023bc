<template>
  <view class="favorites-page">
    <NavBar title="我的收藏" :show-back="true" />
    
    <scroll-view class="content" scroll-y="true">
      <view v-if="favoriteList.length > 0" class="favorites-list">
        <view 
          v-for="item in favoriteList" 
          :key="item.id" 
          class="favorite-item"
          @click="goToDetail(item.id)"
        >
          <image class="item-cover" :src="item.cover" mode="aspectFill" />
          <view class="item-content">
            <text class="item-title">{{ item.title }}</text>
            <text class="item-desc">{{ item.description }}</text>
            <view class="item-meta">
              <text class="item-duration">{{ item.duration }}</text>
              <text class="item-learners">{{ item.learners }}人正在学</text>
            </view>
          </view>
          <view class="item-action" @click.stop="removeFavorite(item.id)">
            <text class="remove-icon">❤️</text>
          </view>
        </view>
      </view>
      
      <view v-else class="empty-state">
        <text class="empty-icon">💔</text>
        <text class="empty-title">暂无收藏</text>
        <text class="empty-desc">去探索页面收藏你喜欢的冥想课程吧</text>
        <button class="explore-btn" @click="goToExplore">去探索</button>
      </view>
    </scroll-view>
    
    <TabBar />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import NavBar from '@/components/NavBar.vue'
import TabBar from '@/components/TabBar.vue'

interface FavoriteItem {
  id: string
  title: string
  description: string
  cover: string
  duration: string
  learners: number
  favoriteTime: string
}

// 收藏列表数据
const favoriteList = ref<FavoriteItem[]>([
  {
    id: '101',
    title: '呼吸觉察冥想',
    description: '通过专注于呼吸的节奏，帮助你建立与当下的连接',
    cover: 'https://picsum.photos/120/120?random=101',
    duration: '15分钟',
    learners: 1234,
    favoriteTime: '2024-01-15'
  },
  {
    id: '201',
    title: '深度放松冥想',
    description: '通过渐进式肌肉放松技巧，帮助你释放身体和心理的紧张感',
    cover: 'https://picsum.photos/120/120?random=201',
    duration: '20分钟',
    learners: 2156,
    favoriteTime: '2024-01-10'
  },
  {
    id: '301',
    title: '单点专注冥想',
    description: '通过专注于单一对象，训练心智的专注能力和稳定性',
    cover: 'https://picsum.photos/120/120?random=301',
    duration: '25分钟',
    learners: 1098,
    favoriteTime: '2024-01-08'
  }
])

// 跳转到详情页
const goToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/meditation-detail/meditation-detail?id=${id}`
  })
}

// 取消收藏
const removeFavorite = (id: string) => {
  uni.showModal({
    title: '提示',
    content: '确定要取消收藏吗？',
    success: (res) => {
      if (res.confirm) {
        const index = favoriteList.value.findIndex(item => item.id === id)
        if (index > -1) {
          favoriteList.value.splice(index, 1)
          uni.showToast({
            title: '已取消收藏',
            icon: 'success'
          })
        }
      }
    }
  })
}

// 去探索页面
const goToExplore = () => {
  uni.switchTab({
    url: '/pages/explore/explore'
  })
}
</script>

<style scoped>
.favorites-page {
  min-height: 100vh;
  background: #f7fafc;
}

.content {
  padding-top: 176rpx;
  padding-bottom: 100rpx;
  height: calc(100vh - 276rpx);
}

.favorites-list {
  padding: 32rpx;
}

.favorite-item {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.item-cover {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
}

.item-content {
  flex: 1;
}

.item-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.item-desc {
  display: block;
  font-size: 24rpx;
  color: #718096;
  line-height: 1.4;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.item-duration {
  font-size: 22rpx;
  color: #7fb069;
  font-weight: 500;
}

.item-learners {
  font-size: 22rpx;
  color: #a0aec0;
}

.item-action {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-icon {
  font-size: 32rpx;
  color: #ff6b9d;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #718096;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

.explore-btn {
  width: 200rpx;
  height: 80rpx;
  background: #7fb069;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.explore-btn:active {
  opacity: 0.8;
}
</style>