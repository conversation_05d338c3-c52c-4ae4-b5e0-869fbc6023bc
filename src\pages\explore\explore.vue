<template>
  <view class="explore-page">
    <NavBar title="探索" :show-back="false" />

    <!-- 搜索框 -->
    <SearchBox />

    <!-- 广告轮播图 -->
    <AdCarousel :ads="adList" @click="onAdClick" />

    <!-- 分类按钮 -->
    <view class="category-buttons">
      <view class="category-btn" @click="onCategoryClick('meditation')">
        <view class="category-icon">🧘</view>
        <text class="category-text">冥想</text>
      </view>
      <view class="category-btn" @click="onCategoryClick('sleep')">
        <view class="category-icon">😴</view>
        <text class="category-text">睡眠</text>
      </view>
      <view class="category-btn" @click="onCategoryClick('sound')">
        <view class="category-icon">🎵</view>
        <text class="category-text">声音</text>
      </view>
    </view>

    <!-- 冥想课程分类 -->
    <scroll-view class="content" scroll-y="true">
      <MeditationSection v-for="section in meditationSections" :key="section.id" :title="section.title"
        :items="section.items" :view-all-text="section.viewAllText" :is-more-section="section.isMoreSection"
        @view-all="onViewAll" @card-click="onCardClick" />
    </scroll-view>

    <TabBar />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import NavBar from '@/components/NavBar.vue'
import TabBar from '@/components/TabBar.vue'
import SearchBox from '@/components/SearchBox.vue'
import AdCarousel from '@/components/AdCarousel.vue'
import MeditationSection from '@/components/MeditationSection.vue'

// 分类点击处理
const onCategoryClick = (category: string) => {
  uni.navigateTo({
    url: `/pages/category/category?type=${category}`
  })
}

// 广告数据
const adList = ref([
  {
    id: '1',
    title: '7天冥想挑战',
    description: '开启你的冥想之旅，每天10分钟',
    image: 'https://picsum.photos/400/200?random=1'
  },
  {
    id: '2',
    title: '深度睡眠冥想',
    description: '让你拥有更好的睡眠质量',
    image: 'https://picsum.photos/400/200?random=2'
  },
  {
    id: '3',
    title: '专注力提升',
    description: '通过冥想提高工作效率',
    image: 'https://picsum.photos/400/200?random=3'
  }
])

// 冥想课程分类数据
const meditationSections = ref([
  {
    id: '1',
    title: '正念冥想',
    items: [
      {
        id: '101',
        title: '呼吸觉察',
        cover: 'https://picsum.photos/240/160?random=101',
        learners: 1234
      },
      {
        id: '102',
        title: '身体扫描',
        cover: 'https://picsum.photos/240/160?random=102',
        learners: 856
      },
      {
        id: '103',
        title: '行走冥想',
        cover: 'https://picsum.photos/240/160?random=103',
        learners: 672
      },
      {
        id: '104',
        title: '饮食冥想',
        cover: 'https://picsum.photos/240/160?random=104',
        learners: 543
      }
    ]
  },
  {
    id: '2',
    title: '入眠冥想',
    items: [
      {
        id: '201',
        title: '深度放松',
        cover: 'https://picsum.photos/240/160?random=201',
        learners: 2156
      },
      {
        id: '202',
        title: '渐进式肌肉放松',
        cover: 'https://picsum.photos/240/160?random=202',
        learners: 1789
      },
      {
        id: '203',
        title: '睡前故事',
        cover: 'https://picsum.photos/240/160?random=203',
        learners: 1432
      },
      {
        id: '204',
        title: '白噪音冥想',
        cover: 'https://picsum.photos/240/160?random=204',
        learners: 987
      }
    ]
  },
  {
    id: '3',
    title: '专注冥想',
    items: [
      {
        id: '301',
        title: '单点专注',
        cover: 'https://picsum.photos/240/160?random=301',
        learners: 1098
      },
      {
        id: '302',
        title: '数息冥想',
        cover: 'https://picsum.photos/240/160?random=302',
        learners: 876
      },
      {
        id: '303',
        title: '视觉专注',
        cover: 'https://picsum.photos/240/160?random=303',
        learners: 654
      },
      {
        id: '304',
        title: '声音专注',
        cover: 'https://picsum.photos/240/160?random=304',
        learners: 432
      }
    ]
  },
  {
    id: '4',
    title: '更多课程',
    viewAllText: '全部课程',
    isMoreSection: true,
    items: [
      {
        id: '401',
        title: '情绪管理',
        cover: 'https://picsum.photos/240/160?random=401',
        learners: 1567
      },
      {
        id: '402',
        title: '压力释放',
        cover: 'https://picsum.photos/240/160?random=402',
        learners: 1234
      },
      {
        id: '403',
        title: '自我接纳',
        cover: 'https://picsum.photos/240/160?random=403',
        learners: 987
      },
      {
        id: '404',
        title: '感恩冥想',
        cover: 'https://picsum.photos/240/160?random=404',
        learners: 876
      },
      {
        id: '405',
        title: '慈悲冥想',
        cover: 'https://picsum.photos/240/160?random=405',
        learners: 765
      },
      {
        id: '406',
        title: '创意冥想',
        cover: 'https://picsum.photos/240/160?random=406',
        learners: 654
      },
      {
        id: '407',
        title: '能量提升',
        cover: 'https://picsum.photos/240/160?random=407',
        learners: 543
      },
      {
        id: '408',
        title: '内观冥想',
        cover: 'https://picsum.photos/240/160?random=408',
        learners: 432
      },
      {
        id: '409',
        title: '禅修入门',
        cover: 'https://picsum.photos/240/160?random=409',
        learners: 321
      },
      {
        id: '410',
        title: '瑜伽冥想',
        cover: 'https://picsum.photos/240/160?random=410',
        learners: 210
      }
    ]
  }
])

// 事件处理

const onAdClick = (item: any) => {
  console.log('点击广告:', item)
  // 这里可以添加广告点击逻辑
}

const onViewAll = (title: string) => {
  console.log('查看全部:', title)

  // 根据分类标题映射到对应的分类类型
  const categoryMap: { [key: string]: string } = {
    '正念冥想': 'meditation',
    '入眠冥想': 'sleep',
    '专注冥想': 'meditation',
    '更多课程': 'meditation'
  }

  const categoryType = categoryMap[title] || 'meditation'

  // 跳转到分类页面
  uni.navigateTo({
    url: `/pages/category/category?type=${categoryType}`
  })
}

const onCardClick = (id: string) => {
  console.log('点击课程:', id)
  // 跳转到冥想详情页面
  uni.navigateTo({
    url: `/pages/meditation-detail/meditation-detail?id=${id}`
  })
}
</script>

<style scoped>
.explore-page {
  padding-top: 176rpx;
  /* 状态栏 + 导航栏高度 */

  min-height: 100vh;
  background-color: #F7FAFC;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding-bottom: 100rpx;
  /* TabBar高度 */
  background-color: #F7FAFC;
}

.category-buttons {
  display: flex;
  justify-content: space-around;
  padding: 32rpx;
  background-color: #F7FAFC;
  gap: 60rpx;
}

.category-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background: #ffffff;
  border-radius: 16rpx;
  width: 200rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.category-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.category-text {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 500;
}
</style>