// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { action, userInfo } = event
    const openid = wxContext.OPENID
    
    const userCollection = db.collection('users')
    
    switch (action) {
      case 'get':
        // 获取用户信息
        const userResult = await userCollection.where({
          openid: openid
        }).get()
        
        if (userResult.data.length > 0) {
          return {
            success: true,
            userInfo: userResult.data[0]
          }
        } else {
          return {
            success: false,
            error: '用户不存在'
          }
        }
        
      case 'update':
        // 更新用户信息
        if (!userInfo) {
          return {
            success: false,
            error: '缺少用户信息参数'
          }
        }
        
        const updateData = {
          ...userInfo,
          updateTime: new Date()
        }
        
        const updateResult = await userCollection.where({
          openid: openid
        }).update({
          data: updateData
        })
        
        if (updateResult.stats.updated > 0) {
          // 获取更新后的用户信息
          const updatedUser = await userCollection.where({
            openid: openid
          }).get()
          
          return {
            success: true,
            userInfo: updatedUser.data[0]
          }
        } else {
          return {
            success: false,
            error: '更新失败'
          }
        }
        
      case 'updateStats':
        // 更新用户统计数据（多肉数量、冥想等级、坚持天数等）
        const { succulentCount, meditationLevel, totalDays, weekData } = event
        
        const statsUpdateData = {}
        if (succulentCount !== undefined) statsUpdateData.succulentCount = succulentCount
        if (meditationLevel !== undefined) statsUpdateData.meditationLevel = meditationLevel
        if (totalDays !== undefined) statsUpdateData.totalDays = totalDays
        if (weekData !== undefined) statsUpdateData.weekData = weekData
        
        statsUpdateData.updateTime = new Date()
        
        const statsResult = await userCollection.where({
          openid: openid
        }).update({
          data: statsUpdateData
        })
        
        return {
          success: statsResult.stats.updated > 0,
          updated: statsResult.stats.updated
        }
        
      default:
        return {
          success: false,
          error: '不支持的操作类型'
        }
    }
  } catch (error) {
    console.error('用户信息操作失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
