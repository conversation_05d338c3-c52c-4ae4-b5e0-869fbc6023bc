// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { code, userInfo } = event
    
    // 如果没有传入code，直接返回openid
    if (!code) {
      return {
        success: true,
        openid: wxContext.OPENID,
        appid: wxContext.APPID,
        unionid: wxContext.UNIONID,
      }
    }

    // 获取用户openid
    const openid = wxContext.OPENID
    const appid = wxContext.APPID
    const unionid = wxContext.UNIONID

    // 查询用户是否已存在
    const userCollection = db.collection('users')
    const existingUser = await userCollection.where({
      openid: openid
    }).get()

    let userData = {
      openid: openid,
      appid: appid,
      unionid: unionid,
      lastLoginTime: new Date(),
      createTime: new Date()
    }

    // 如果传入了用户信息，则更新用户信息
    if (userInfo) {
      userData = {
        ...userData,
        nickname: userInfo.nickname || '微信用户',
        avatar: userInfo.avatar || '',
        gender: userInfo.gender || 0,
        city: userInfo.city || '',
        province: userInfo.province || '',
        country: userInfo.country || '',
        language: userInfo.language || 'zh_CN'
      }
    }

    let result
    if (existingUser.data.length > 0) {
      // 用户已存在，更新登录时间和用户信息
      const updateData = {
        lastLoginTime: new Date()
      }
      
      if (userInfo) {
        Object.assign(updateData, {
          nickname: userInfo.nickname || existingUser.data[0].nickname,
          avatar: userInfo.avatar || existingUser.data[0].avatar,
          gender: userInfo.gender !== undefined ? userInfo.gender : existingUser.data[0].gender,
          city: userInfo.city || existingUser.data[0].city,
          province: userInfo.province || existingUser.data[0].province,
          country: userInfo.country || existingUser.data[0].country,
          language: userInfo.language || existingUser.data[0].language
        })
      }

      result = await userCollection.doc(existingUser.data[0]._id).update({
        data: updateData
      })
      
      userData = {
        ...existingUser.data[0],
        ...updateData,
        _id: existingUser.data[0]._id
      }
    } else {
      // 新用户，创建用户记录
      result = await userCollection.add({
        data: userData
      })
      userData._id = result._id
    }

    return {
      success: true,
      openid: openid,
      appid: appid,
      unionid: unionid,
      userInfo: userData,
      isNewUser: existingUser.data.length === 0
    }
  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      error: error.message,
      openid: wxContext.OPENID
    }
  }
}
