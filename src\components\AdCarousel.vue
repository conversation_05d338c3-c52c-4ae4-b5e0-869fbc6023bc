<template>
  <view class="ad-carousel">
    <swiper 
      class="swiper" 
      :indicator-dots="true" 
      :autoplay="true" 
      :interval="3000"
      :duration="500"
      indicator-color="rgba(255, 255, 255, 0.3)"
      indicator-active-color="#ffffff"
    >
      <swiper-item v-for="(item, index) in ads" :key="index">
        <view class="ad-item" @click="onAdClick(item)">
          <image class="ad-image" :src="item.image" mode="aspectFill" />
          <view class="ad-content">
            <text class="ad-title">{{ item.title }}</text>
            <text class="ad-desc">{{ item.description }}</text>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script setup lang="ts">
interface AdItem {
  id: string
  title: string
  description: string
  image: string
  link?: string
}

interface Props {
  ads?: AdItem[]
}

interface Emits {
  (e: 'click', item: AdItem): void
}

const props = withDefaults(defineProps<Props>(), {
  ads: () => [
    {
      id: '1',
      title: '7天冥想挑战',
      description: '开启你的冥想之旅',
      image: 'https://picsum.photos/400/200?random=1'
    },
    {
      id: '2', 
      title: '深度睡眠冥想',
      description: '让你拥有更好的睡眠质量',
      image: 'https://picsum.photos/400/200?random=2'
    },
    {
      id: '3',
      title: '专注力提升',
      description: '通过冥想提高工作效率',
      image: 'https://picsum.photos/400/200?random=3'
    }
  ]
})

const emit = defineEmits<Emits>()

const onAdClick = (item: AdItem) => {
  emit('click', item)
}
</script>

<style scoped>
.ad-carousel {
  margin: 24rpx 32rpx;
}

.swiper {
  height: 320rpx;
  border-radius: 24rpx;
  overflow: hidden;
}

.ad-item {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.ad-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ad-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 48rpx 32rpx 32rpx;
  color: #ffffff;
}

.ad-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.ad-desc {
  font-size: 24rpx;
  opacity: 0.9;
}
</style>