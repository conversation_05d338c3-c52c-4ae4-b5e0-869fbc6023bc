<template>
  <view class="plan-card" :class="{ 'completed': isCompleted }" @click="onClick">
    <view class="cover-container">
      <image class="card-cover" :src="cover" mode="aspectFill" />
    </view>
    <view class="card-content">
      <text class="course-name">{{ courseName }}</text>
      <text class="lesson-duration">{{ lessonName }} · {{ duration }}分钟</text>
      <view class="progress-section">
        <view class="learners-info">
          <text class="learners-count">已完成1/10节课</text>
          <text class="learners-count">{{ learnersCount }}人在学</text>
        </view>
        <view class="progress-bar-container">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: progress + '%' }"></view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="isCompleted" class="completed-badge">
      <text class="completed-text">✓</text>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  id: string
  courseName: string
  lessonName: string
  cover: string
  duration: number
  progress: number
  learnersCount: number
  isCompleted?: boolean
}

interface Emits {
  (e: 'click', id: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const onClick = () => {
  emit('click', props.id)
}
</script>

<style scoped>
.plan-card {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 24rpx;
  display: flex;
  position: relative;
  padding: 20rpx;
}

.plan-card.completed {
  opacity: 0.7;
}

.cover-container {
  margin-right: 20rpx;
}

.card-cover {
  width: 120rpx;
  height: 120rpx;
  object-fit: cover;
  border-radius: 12rpx;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 120rpx;
}

.course-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.lesson-duration {
  font-size: 24rpx;
  color: #4a5568;
  margin-bottom: 8rpx;
}

.progress-section {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.learners-info {
  display: flex;
  justify-content: space-between;
}

.learners-count {
  font-size: 20rpx;
  color: #a0aec0;
}

.progress-bar-container {
  display: flex;
  align-items: center;
}

.progress-bar {
  flex: 1;
  height: 6rpx;
  background: #e2e8f0;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #7fb069;
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.completed-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 36rpx;
  height: 36rpx;
  background: #7fb069;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.completed-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: bold;
}
</style>