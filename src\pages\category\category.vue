<template>
  <view class="category-page">
    <NavBar :title="categoryTitle" />

    <!-- 横向滑动标签 -->
    <scroll-view class="tabs-container" scroll-x="true">
      <view class="tabs">
        <view v-for="(tab, index) in tabs" :key="tab.id" class="tab-item" :class="{ active: activeTabIndex === index }"
          @click="onTabClick(index)">
          {{ tab.name }}
        </view>
      </view>
    </scroll-view>

    <!-- 内容区域 -->
    <scroll-view class="content" scroll-y="true">
      <view class="content-grid">
        <ContentCard v-for="item in currentTabItems" :key="item.id" :id="item.id" :title="item.title"
          :cover="item.cover" :learners="item.learners" @click="onItemClick" />
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import NavBar from '@/components/NavBar.vue'
import ContentCard from '@/components/ContentCard.vue'
import { onLoad } from '@dcloudio/uni-app'

// 页面参数
const categoryType = ref('')
const activeTabIndex = ref(0)

// 分类标题映射
const categoryTitleMap = {
  meditation: '冥想',
  sleep: '睡眠',
  sound: '声音'
}

const categoryTitle = computed(() => {
  return categoryTitleMap[categoryType.value as keyof typeof categoryTitleMap] || '分类'
})

// 标签数据
const tabsMap = {
  meditation: [
    { id: 'mindfulness', name: '正念冥想' },
    { id: 'focus', name: '专注冥想' },
    { id: 'emotion', name: '情绪管理' },
    { id: 'stress', name: '压力释放' }
  ],
  sleep: [
    { id: 'deep', name: '深度睡眠' },
    { id: 'story', name: '睡前故事' },
    { id: 'relax', name: '放松冥想' },
    { id: 'nature', name: '自然声音' }
  ],
  sound: [
    { id: 'nature', name: '自然声音' },
    { id: 'white', name: '白噪音' },
    { id: 'music', name: '冥想音乐' },
    { id: 'binaural', name: '双耳节拍' }
  ]
}

const tabs = computed(() => {
  return tabsMap[categoryType.value as keyof typeof tabsMap] || []
})

// 内容数据
const contentMap = {
  meditation: {
    mindfulness: [
      { id: '101', title: '呼吸觉察', cover: 'https://picsum.photos/240/160?random=101', learners: 1234 },
      { id: '102', title: '身体扫描', cover: 'https://picsum.photos/240/160?random=102', learners: 856 },
      { id: '103', title: '行走冥想', cover: 'https://picsum.photos/240/160?random=103', learners: 672 },
      { id: '104', title: '饮食冥想', cover: 'https://picsum.photos/240/160?random=104', learners: 543 }
    ],
    focus: [
      { id: '301', title: '单点专注', cover: 'https://picsum.photos/240/160?random=301', learners: 1098 },
      { id: '302', title: '数息冥想', cover: 'https://picsum.photos/240/160?random=302', learners: 876 },
      { id: '303', title: '视觉专注', cover: 'https://picsum.photos/240/160?random=303', learners: 654 },
      { id: '304', title: '声音专注', cover: 'https://picsum.photos/240/160?random=304', learners: 432 }
    ],
    emotion: [
      { id: '401', title: '情绪管理', cover: 'https://picsum.photos/240/160?random=401', learners: 1567 },
      { id: '405', title: '慈悲冥想', cover: 'https://picsum.photos/240/160?random=405', learners: 765 },
      { id: '403', title: '自我接纳', cover: 'https://picsum.photos/240/160?random=403', learners: 987 },
      { id: '404', title: '感恩冥想', cover: 'https://picsum.photos/240/160?random=404', learners: 876 }
    ],
    stress: [
      { id: '402', title: '压力释放', cover: 'https://picsum.photos/240/160?random=402', learners: 1234 },
      { id: '407', title: '能量提升', cover: 'https://picsum.photos/240/160?random=407', learners: 543 },
      { id: '406', title: '创意冥想', cover: 'https://picsum.photos/240/160?random=406', learners: 654 },
      { id: '408', title: '内观冥想', cover: 'https://picsum.photos/240/160?random=408', learners: 432 }
    ]
  },
  sleep: {
    deep: [
      { id: '201', title: '深度放松', cover: 'https://picsum.photos/240/160?random=201', learners: 2156 },
      { id: '202', title: '渐进式肌肉放松', cover: 'https://picsum.photos/240/160?random=202', learners: 1789 },
      { id: '204', title: '白噪音冥想', cover: 'https://picsum.photos/240/160?random=204', learners: 987 },
      { id: '205', title: '深度睡眠引导', cover: 'https://picsum.photos/240/160?random=205', learners: 1456 }
    ],
    story: [
      { id: '203', title: '睡前故事', cover: 'https://picsum.photos/240/160?random=203', learners: 1432 },
      { id: '206', title: '森林童话', cover: 'https://picsum.photos/240/160?random=206', learners: 1123 },
      { id: '207', title: '海边故事', cover: 'https://picsum.photos/240/160?random=207', learners: 998 },
      { id: '208', title: '星空传说', cover: 'https://picsum.photos/240/160?random=208', learners: 876 }
    ],
    relax: [
      { id: '209', title: '全身放松', cover: 'https://picsum.photos/240/160?random=209', learners: 1567 },
      { id: '210', title: '心灵放松', cover: 'https://picsum.photos/240/160?random=210', learners: 1234 },
      { id: '211', title: '温暖拥抱', cover: 'https://picsum.photos/240/160?random=211', learners: 987 },
      { id: '212', title: '安全感冥想', cover: 'https://picsum.photos/240/160?random=212', learners: 765 }
    ],
    nature: [
      { id: '213', title: '雨声入眠', cover: 'https://picsum.photos/240/160?random=213', learners: 2345 },
      { id: '214', title: '海浪声', cover: 'https://picsum.photos/240/160?random=214', learners: 1876 },
      { id: '215', title: '森林鸟鸣', cover: 'https://picsum.photos/240/160?random=215', learners: 1543 },
      { id: '216', title: '篝火声', cover: 'https://picsum.photos/240/160?random=216', learners: 1234 }
    ]
  },
  sound: {
    nature: [
      { id: '501', title: '雨声', cover: 'https://picsum.photos/240/160?random=501', learners: 3456 },
      { id: '502', title: '海浪', cover: 'https://picsum.photos/240/160?random=502', learners: 2876 },
      { id: '503', title: '鸟鸣', cover: 'https://picsum.photos/240/160?random=503', learners: 2543 },
      { id: '504', title: '风声', cover: 'https://picsum.photos/240/160?random=504', learners: 2234 }
    ],
    white: [
      { id: '505', title: '白噪音', cover: 'https://picsum.photos/240/160?random=505', learners: 1876 },
      { id: '506', title: '粉噪音', cover: 'https://picsum.photos/240/160?random=506', learners: 1654 },
      { id: '507', title: '棕噪音', cover: 'https://picsum.photos/240/160?random=507', learners: 1432 },
      { id: '508', title: '蓝噪音', cover: 'https://picsum.photos/240/160?random=508', learners: 1234 }
    ],
    music: [
      { id: '509', title: '西藏颂钵', cover: 'https://picsum.photos/240/160?random=509', learners: 2567 },
      { id: '510', title: '水晶钵', cover: 'https://picsum.photos/240/160?random=510', learners: 2234 },
      { id: '511', title: '古典音乐', cover: 'https://picsum.photos/240/160?random=511', learners: 1987 },
      { id: '512', title: '环境音乐', cover: 'https://picsum.photos/240/160?random=512', learners: 1765 }
    ],
    binaural: [
      { id: '513', title: 'Alpha波', cover: 'https://picsum.photos/240/160?random=513', learners: 1543 },
      { id: '514', title: 'Theta波', cover: 'https://picsum.photos/240/160?random=514', learners: 1432 },
      { id: '515', title: 'Delta波', cover: 'https://picsum.photos/240/160?random=515', learners: 1321 },
      { id: '516', title: 'Gamma波', cover: 'https://picsum.photos/240/160?random=516', learners: 1234 }
    ]
  }
}

const currentTabItems = computed(() => {
  const categoryData = contentMap[categoryType.value as keyof typeof contentMap]
  if (!categoryData) return []

  const currentTab = tabs.value[activeTabIndex.value]
  if (!currentTab) return []

  return categoryData[currentTab.id as keyof typeof categoryData] || []
})

// 事件处理
const onTabClick = (index: number) => {
  activeTabIndex.value = index
}

const onItemClick = (id: string) => {
  uni.navigateTo({
    url: `/pages/meditation-detail/meditation-detail?id=${id}`
  })
}

onLoad((options: any) => {
  if (options.type) {
    categoryType.value = options.type
  }
})
</script>

<style scoped>
.category-page {
  padding-top: 176rpx;
  min-height: 100vh;
  background-color: #F7FAFC;
}

.tabs-container {
  background: #ffffff;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #e2e8f0;
}

.tabs {
  display: flex;
  padding: 0 32rpx;
  white-space: nowrap;
}

.tab-item {
  padding: 16rpx 32rpx;
  margin-right: 24rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  color: #718096;
  background: #f7fafc;
  white-space: nowrap;
  transition: all 0.3s;
}

.tab-item.active {
  background: #7FB069;
  color: #ffffff;
}

.content {
  flex: 1;
  padding: 32rpx 24rpx;
  width: calc(100% - 48rpx);
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.content-grid :deep(.content-card) {
  width: 100%;
}
</style>