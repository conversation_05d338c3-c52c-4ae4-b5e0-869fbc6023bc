// 用户认证相关工具函数

// 声明微信小程序全局对象
declare const wx: any;

export interface UserInfo {
  _id?: string;
  id: string;
  openid?: string;
  nickname: string;
  avatar: string;
  gender: number;
  city: string;
  province: string;
  country: string;
  language: string;
  succulentCount?: number;
  meditationLevel?: number;
  totalDays?: number;
  createTime?: Date;
  lastLoginTime?: Date;
  updateTime?: Date;
}

// 初始化云开发
const initCloud = () => {
  // #ifdef MP-WEIXIN
  if (typeof wx !== 'undefined' && wx.cloud) {
    wx.cloud.init({
      env: 'cloud1-6gxijqna174103d8', // 云开发环境ID
      traceUser: true
    })
  }
  // #endif
}

// 获取微信用户信息并调用云函数登录
export const getWechatUserInfo = (): Promise<UserInfo> => {
  return new Promise((resolve, reject) => {
    // 初始化云开发
    initCloud()

    // #ifdef MP-WEIXIN
    uni.getUserProfile({
      desc: "用于完善用户资料",
      success: async (res) => {
        try {
          const userInfo = {
            nickname: res.userInfo.nickName || "微信用户",
            avatar: res.userInfo.avatarUrl || "/static/icons/avatar.png",
            gender: res.userInfo.gender || 0,
            city: res.userInfo.city || "",
            province: res.userInfo.province || "",
            country: res.userInfo.country || "",
            language: res.userInfo.language || "zh_CN",
          };

          // 调用云函数进行登录和用户信息存储
          const loginResult = await wx.cloud.callFunction({
            name: 'login',
            data: {
              userInfo: userInfo
            }
          })

          if (loginResult.result.success) {
            const cloudUserInfo: UserInfo = {
              _id: loginResult.result.userInfo._id,
              id: loginResult.result.openid,
              openid: loginResult.result.openid,
              nickname: loginResult.result.userInfo.nickname,
              avatar: loginResult.result.userInfo.avatar,
              gender: loginResult.result.userInfo.gender,
              city: loginResult.result.userInfo.city,
              province: loginResult.result.userInfo.province,
              country: loginResult.result.userInfo.country,
              language: loginResult.result.userInfo.language,
              succulentCount: loginResult.result.userInfo.succulentCount || 0,
              meditationLevel: loginResult.result.userInfo.meditationLevel || 1,
              totalDays: loginResult.result.userInfo.totalDays || 0,
              createTime: loginResult.result.userInfo.createTime,
              lastLoginTime: loginResult.result.userInfo.lastLoginTime,
            };

            // 保存用户信息到本地存储
            uni.setStorageSync("userInfo", cloudUserInfo);
            uni.setStorageSync("openid", loginResult.result.openid);
            resolve(cloudUserInfo);
          } else {
            throw new Error(loginResult.result.error || '登录失败');
          }
        } catch (error) {
          console.error("云函数登录失败:", error);
          reject(error);
        }
      },
      fail: (err) => {
        console.error("获取用户信息失败:", err);
        reject(err);
      },
    });
    // #endif

    // #ifndef MP-WEIXIN
    // 非微信小程序环境，返回模拟数据
    const mockUserInfo: UserInfo = {
      id: generateUserId(),
      nickname: "多肉爱好者",
      avatar: "/static/icons/avatar.png",
      gender: 1,
      city: "深圳",
      province: "广东",
      country: "中国",
      language: "zh_CN",
      succulentCount: 12,
      meditationLevel: 5,
      totalDays: 28,
    };
    uni.setStorageSync("userInfo", mockUserInfo);
    resolve(mockUserInfo);
    // #endif
  });
};

// 微信登录（调用云函数）
export const wechatLogin = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    // 初始化云开发
    initCloud()

    // #ifdef MP-WEIXIN
    uni.login({
      provider: "weixin",
      success: async (res) => {
        if (res.code) {
          try {
            console.log("登录成功，code:", res.code);

            // 调用云函数获取openid
            const loginResult = await wx.cloud.callFunction({
              name: 'login',
              data: {
                code: res.code
              }
            })

            if (loginResult.result.success) {
              const openid = loginResult.result.openid;
              uni.setStorageSync("openid", openid);
              uni.setStorageSync("token", openid); // 使用openid作为token
              resolve(openid);
            } else {
              throw new Error(loginResult.result.error || '获取openid失败');
            }
          } catch (error) {
            console.error("云函数登录失败:", error);
            reject(error);
          }
        } else {
          reject(new Error("登录失败"));
        }
      },
      fail: (err) => {
        console.error("登录失败:", err);
        reject(err);
      },
    });
    // #endif

    // #ifndef MP-WEIXIN
    // 非微信小程序环境，返回模拟token
    const mockToken = "mock_token_" + Date.now();
    uni.setStorageSync("token", mockToken);
    resolve(mockToken);
    // #endif
  });
};

// 生成用户ID
const generateUserId = (): string => {
  return (
    "user_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11)
  );
};

// 获取本地存储的用户信息
export const getStoredUserInfo = (): UserInfo | null => {
  try {
    const userInfo = uni.getStorageSync("userInfo");
    return userInfo || null;
  } catch (error) {
    console.error("获取本地用户信息失败:", error);
    return null;
  }
};

// 获取本地存储的token
export const getStoredToken = (): string | null => {
  try {
    const token = uni.getStorageSync("token");
    return token || null;
  } catch (error) {
    console.error("获取本地token失败:", error);
    return null;
  }
};

// 清除用户信息
export const clearUserInfo = (): void => {
  try {
    uni.removeStorageSync("userInfo");
    uni.removeStorageSync("token");
    uni.removeStorageSync("openid");
  } catch (error) {
    console.error("清除用户信息失败:", error);
  }
};

// 检查是否已登录
export const isLoggedIn = (): boolean => {
  const token = getStoredToken();
  const userInfo = getStoredUserInfo();
  return !!(token && userInfo);
};

// 获取云端用户信息
export const getCloudUserInfo = (): Promise<UserInfo> => {
  return new Promise((resolve, reject) => {
    // 初始化云开发
    initCloud()

    // #ifdef MP-WEIXIN
    wx.cloud.callFunction({
      name: 'getUserInfo',
      data: {
        action: 'get'
      },
      success: (res: any) => {
        if (res.result.success) {
          const userInfo = res.result.userInfo;
          // 更新本地存储
          uni.setStorageSync("userInfo", userInfo);
          resolve(userInfo);
        } else {
          reject(new Error(res.result.error || '获取用户信息失败'));
        }
      },
      fail: (error: any) => {
        console.error("获取云端用户信息失败:", error);
        reject(error);
      }
    });
    // #endif

    // #ifndef MP-WEIXIN
    // 非微信小程序环境，返回本地存储的信息
    const localUserInfo = getStoredUserInfo();
    if (localUserInfo) {
      resolve(localUserInfo);
    } else {
      reject(new Error('未找到用户信息'));
    }
    // #endif
  });
};

// 更新用户统计数据
export const updateUserStats = (stats: {
  succulentCount?: number;
  meditationLevel?: number;
  totalDays?: number;
  weekData?: any;
}): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    // 初始化云开发
    initCloud()

    // #ifdef MP-WEIXIN
    wx.cloud.callFunction({
      name: 'getUserInfo',
      data: {
        action: 'updateStats',
        ...stats
      },
      success: (res: any) => {
        if (res.result.success) {
          // 更新本地用户信息
          const localUserInfo = getStoredUserInfo();
          if (localUserInfo) {
            const updatedUserInfo = { ...localUserInfo, ...stats };
            uni.setStorageSync("userInfo", updatedUserInfo);
          }
          resolve(true);
        } else {
          reject(new Error(res.result.error || '更新统计数据失败'));
        }
      },
      fail: (error: any) => {
        console.error("更新用户统计数据失败:", error);
        reject(error);
      }
    });
    // #endif

    // #ifndef MP-WEIXIN
    // 非微信小程序环境，只更新本地存储
    const localUserInfo = getStoredUserInfo();
    if (localUserInfo) {
      const updatedUserInfo = { ...localUserInfo, ...stats };
      uni.setStorageSync("userInfo", updatedUserInfo);
      resolve(true);
    } else {
      reject(new Error('未找到用户信息'));
    }
    // #endif
  });
};
